from django.shortcuts import render
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from core.controller.user.authentication import *
from core.controller.user.institute import *
from core.controller.user.student_manager import *
from examination.controller.examination_manager import *
from core.controller.utils.reports_manager import *
from staffmanagement.controller.staff_manager import *
from core.controller.user.notification_manager import *
from core.controller.utils.tracking_events import *
from core.controller.client.institute_payment_manager import *
from .controller.institute_management_manager import *
from user_management.controller.user_management_manager import *
from math import *

module_name = 'INSTITUTE_MANAGEMENT'

def authorized_user_session(request):
	return authorized_module(request,module_name)

def dashboard_view(request, institute_unique_code):
	if authorized_user_session(request):
		track_event(request, institute_unique_code, {"channel" : "WEB","trackingEventName" : "INSTITUTE_MANAGEMENT_MODULE_LOADED"})
		user_login_view = get_user_login_view(request)
		bell_notification_count = get_bell_notification_count(user_login_view, institute_unique_code)
		payment_status_data = get_payment_status_data(request)
		view_only_access = is_user_view_only(request)
		app_attributes = get_app_attributes(user_login_view, institute_unique_code)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		web_ui_preferences = get_web_ui_preferences(user_login_view, institute_unique_code)
		user_type = user_login_view['user']['userType']
		tutorial_videos = get_tutorial_video_details(module_name, institute_unique_code, user_type)
		return render(request, 'institute_management/layouts/default-institute-management-layout.html',{'dashboard_theme' : get_user_theme(request), 'app_attributes': app_attributes, 'user': user_login_view['user'], 'institute': get_institute_details(user_login_view, institute_unique_code), 'payment_status_data' :  payment_status_data, 'bell_notification_count' : bell_notification_count, 'academic_years':academic_years, 'current_session':current_session, 'web_ui_preferences' : web_ui_preferences, 'view_only_access' : view_only_access, 'tutorial_videos': tutorial_videos})

def home_page_view(request, institute_unique_code):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		return render(request, 'institute_management/layouts/dashboard-content.html',{'academic_years':academic_years, 'current_session':current_session})

def home_page_session_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) :
		user_login_view = get_user_login_view(request)
		return render(request, 'institute_management/layouts/dashboard-session-content.html',{'user': user_login_view['user']})

def manage_sections_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		academic_session_id = current_session['academicSessionId']
		institute_standard_sections_list = get_standards(user_login_view, institute_unique_code,academic_session_id)
		return render(request, 'institute_management/manage_sections/manage_sections_details.html',{'institute_standard_sections_list':institute_standard_sections_list,'academic_years':academic_years, 'current_session':current_session})

def	manage_sections_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_standard_sections_list = get_standards(user_login_view, institute_unique_code,academic_session_id)
		class_students = get_class_students(user_login_view, institute_unique_code, academic_session_id, request.GET.get("standardId",""))
		if(request.GET.get("sectionIds")!=None):
			class_students =  get_class_student_list(user_login_view, institute_unique_code, academic_session_id, request.GET.get("standardId",""), request.GET.get("sectionIds",""))
		return render(request, 'institute_management/manage_sections/manage_sections_list.html',{'institute_standard_sections_list':institute_standard_sections_list,"class_students":class_students})

@csrf_exempt
def update_standard_section_view(request, institute_unique_code,academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		bulk_update_students_section_data = json.loads(request.POST['bulkUpdateStudentSections'])
		response_data = update_standard_section(user_login_view, institute_unique_code, bulk_update_students_section_data,academic_session_id)
		return render(request, 'institute_management/manage_sections/manage_sections_status_modal.html',{"data":response_data})

@csrf_exempt
def add_standard_section_view(request, institute_unique_code,academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		bulk_add_students_section_data = json.loads(request.POST['bulkAddStudentSections'])
		response_data = add_standard_section(user_login_view, institute_unique_code, bulk_add_students_section_data,academic_session_id)
		return render(request, 'institute_management/manage_sections/manage_sections_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_standard_section_view(request, institute_unique_code,academic_session_id,standard_id,section_ids,student_assign_section_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		response_data = delete_standard_section(user_login_view, institute_unique_code,academic_session_id,standard_id,section_ids,student_assign_section_id)
		return render(request, 'institute_management/manage_sections/manage_sections_status_modal.html',{"data":response_data})

@csrf_exempt
def update_standards_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		# standards_data = json.loads(request.body.decode('utf-8'))['standardsData']
		# standards_list = json.loads(standards_data)
		standards_list = json.loads(request.POST['standardsData'])
		print(standards_list)
		response_data = update_standards(user_login_view, institute_unique_code, standards_list, academic_session_id)
		return render(request, 'institute_management/manage_sections/manage_sections_status_modal.html',{"data":response_data})

def manage_class_teachers_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		academic_years = get_academic_years(user_login_view, institute_unique_code)
		current_session = get_current_academic_year(request, academic_years)
		academic_session_id = current_session['academicSessionId']
		standards_with_class_teacher_details = get_standards_with_class_teacher(user_login_view, institute_unique_code, academic_session_id)
		staff_details = get_organisation_staff_details_by_staff_status(user_login_view, institute_unique_code, "ONBOARD", "ENABLED")
		return render(request, 'institute_management/manage_class_teachers/manage_class_teachers_details.html',{'standards_with_class_teacher_details':standards_with_class_teacher_details,'academic_years':academic_years, 'current_session':current_session,"staff_details":staff_details})

def	manage_class_teachers_view(request, institute_unique_code, academic_session_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		standards_with_class_teacher_details = get_standards_with_class_teacher(user_login_view, institute_unique_code,academic_session_id)
		staff_details = get_organisation_staff_details_by_staff_status(user_login_view, institute_unique_code, "ONBOARD", "ENABLED")
		return render(request, 'institute_management/manage_class_teachers/manage_class_teachers_list.html',{'standards_with_class_teacher_details':standards_with_class_teacher_details,"staff_details":staff_details})

@csrf_exempt
def update_class_teacher_details_view(request, institute_unique_code, academic_session_id, standard_id, staff_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("section_id", None)
		response_data = update_class_teacher_details(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, staff_id)
		return render(request, 'institute_management/manage_class_teachers/manage_class_teachers_status_modal.html',{"data":response_data})

@csrf_exempt
def delete_class_teacher_details_view(request, institute_unique_code, academic_session_id, standard_id):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		section_id = request.GET.get("section_id", None)
		response_data = delete_class_teacher_details(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id)
		return render(request, 'institute_management/manage_class_teachers/manage_class_teachers_status_modal.html',{"data":response_data})

def manage_institute_details_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
		institute_details = get_institute(institute_id)
		return render(request, 'institute_management/manage_institute/manage_institute_details.html',{'institute_details': institute_details, 'user_login_view' : user_login_view})

def manage_institute_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
		institute_details = get_institute(institute_id)
		return render(request, 'institute_management/manage_institute/manage_institute_list.html',{'institute_details': institute_details, 'user_login_view' : user_login_view})


@csrf_exempt
def update_institute_details_home(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		institutePayload = json.loads(request.POST['institutePayload'])
		response_data = update_institute_details(user_login_view, institute_unique_code, institutePayload)
		return render(request, 'institute_management/manage_institute/manage_institute_details_status_modal.html',{"data":response_data})

def manage_institute_logo_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
		institute_details = get_institute(institute_id)
		return render(request, 'institute_management/manage_institute_logo/manage_institute_logo_home.html',{'institute_details': institute_details})

def manage_institute_logo_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
		institute_details = get_institute(institute_id)
		return render(request, 'institute_management/manage_institute_logo/manage_institute_logo.html',{'institute_details': institute_details})

def document_download_view(request, institute_unique_code, document_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		document = download_document(user_login_view, institute_unique_code, document_id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/octet-stream")
			response['Content-Disposition'] = 'inline; filename='+document['file_name']
			return response

@csrf_exempt
def document_delete_view(request, institute_unique_code, document_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response = delete_document(user_login_view, institute_unique_code, document_id)
		return render(request, 'institute_management/manage_institute_logo/manage_institute_logo_status_modal.html',{"data" : response})

@csrf_exempt
def document_upload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' and request.FILES['document']:
		user_login_view = get_user_login_view(request)
		document = request.FILES['document']
		document_type = request.POST['documentType']
		document_name = request.POST['documentName']
		response = upload_document(user_login_view, institute_unique_code,  document_type, document_name, document)
		return render(request, 'institute_management/manage_institute_logo/manage_institute_logo_status_modal.html',{"data" : response})

@csrf_exempt
def standard_document_upload_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST' and request.FILES['document']:
		user_login_view = get_user_login_view(request)
		document = request.FILES['document']
		academic_session_id = request.POST['academicSessionId']
		standard_id = request.POST['standardId']
		section_id = request.POST['sectionId']
		document_name = request.POST['documentName']
		response = upload_standard_document(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, document_name, document)
		return render(request, 'institute_management/documents/document_upload_status_modal.html',{'response' : response})

def standard_document_download_view(request, institute_unique_code, standard_id, document_id):
	if authorized_user_session(request):
		user_login_view = get_user_login_view(request)
		academic_session_id = request.GET.get("academic_session_id", None)
		section_id = request.GET.get("section_id", None)
		document = download_standard_document(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, document_id)
		if(document is not None):
			response = HttpResponse(document['content'], content_type="application/octet-stream")
			response['Content-Disposition'] = 'inline; filename='+document['file_name']
			return response

@csrf_exempt
def standard_document_delete_view(request, institute_unique_code, standard_id, document_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		academic_session_id = request.GET.get("academic_session_id", None)
		section_id = request.GET.get("section_id", None)
		response = delete_standard_document(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, document_id)
		return render(request, 'institute_management/documents/document_delete_status_modal.html',{'response' : response})

def institute_bank_account_details_home_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_bank_account_details = get_institute_bank_account_details(user_login_view, institute_unique_code)
		return render(request, 'institute_management/bank_details/institute_bank_account_details.html',{'institute_bank_account_details' : institute_bank_account_details})

@csrf_exempt
def add_bank_account_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		instituteBankAccountDetailsPayload = json.loads(request.POST['instituteBankAccountDetailsPayload'])
		response_data = add_bank_account(user_login_view, institute_unique_code, instituteBankAccountDetailsPayload)
		return render(request, 'institute_management/bank_details/bank_account_status_modal.html',{"data":response_data})

def bank_account_details_view(request, institute_unique_code):
	if authorized_user_session(request) and request.is_ajax():
		user_login_view = get_user_login_view(request)
		institute_bank_account_details = get_institute_bank_account_details(user_login_view, institute_unique_code)
		return render(request, 'institute_management/bank_details/bank_account_list.html',{'institute_bank_account_details':institute_bank_account_details})

@csrf_exempt
def delete_bank_account_view(request, institute_unique_code, account_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		response_data = delete_bank_account(user_login_view, institute_unique_code, account_id)
		return render(request, 'institute_management/bank_details/bank_account_status_modal.html',{"data":response_data})

@csrf_exempt
def update_bank_account_view(request, institute_unique_code, account_id):
	if authorized_user_session(request) and request.is_ajax() and request.method == 'POST':
		user_login_view = get_user_login_view(request)
		instituteBankAccountDetailsPayload = json.loads(request.POST['instituteBankAccountDetailsPayload'])
		response_data = update_institute_bank_account_details(user_login_view, institute_unique_code,account_id, instituteBankAccountDetailsPayload)
		return render(request, 'institute_management/bank_details/bank_account_status_modal.html',{"data":response_data})
