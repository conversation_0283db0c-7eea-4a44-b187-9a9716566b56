import time
import json
from core.controller.utils.restclient import *
from core.controller.utils.date_utils import *
from core.controller.user.institute import *

restclient = getrestclient()

def update_standard_section(user_login_view, institute_unique_code, bulk_update_students_section_data,academic_session_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    bulk_update_students_section_data['instituteId'] = institute_id
    response = restclient.put(user_login_view, "/2.0/institute/standard-sections/"+str(institute_id)+"?user_id="+str(user['uuid']),bulk_update_students_section_data)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Sections Updated Successfully!!!"}

def add_standard_section(user_login_view, institute_unique_code, bulk_add_students_section_data,academic_session_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    bulk_add_students_section_data['instituteId'] = institute_id
    response = restclient.post(user_login_view, "/2.0/institute/standard-sections?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid']),bulk_add_students_section_data)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Sections Added Successfully!!!"}

def delete_standard_section(user_login_view, institute_unique_code,academic_session_id,standard_id,section_ids,student_assign_section_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/institute/standard-sections?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid'])+"&standard_id="+str(standard_id)+"&section_ids="+section_ids+"&student_assign_section_id="+student_assign_section_id)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Sections Deleted Successfully!!!"}

def update_standards(user_login_view, institute_unique_code, standards_list, academic_session_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/institute/standards?institute_id="+str(institute_id)+"&user_id="+str(user['uuid']), standards_list)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Standards Updated Successfully!!!"}

def get_standards_with_class_teacher(user_login_view, institute_unique_code, academic_session_id):
	institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
	return restclient.get(user_login_view, "/2.0/institute/standard-staff-details?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)).get_data()

def update_class_teacher_details(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, staff_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/institute/standard-staff-details/"+str(staff_id)+"/"+str(standard_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid'])+"&section_id="+section_id)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Class Teacher Details Updated Successfully!!!"}

def delete_class_teacher_details(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.delete(user_login_view, "/2.0/institute/standard-staff-details/"+str(standard_id)+"?institute_id="+str(institute_id)+"&academic_session_id="+str(academic_session_id)+"&user_id="+str(user['uuid'])+"&section_id="+section_id)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Class Teacher Details Deleted Successfully!!!"}

def update_institute_details(user_login_view, institute_unique_code, institutePayload):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.put(user_login_view, "/2.0/institute/update-details/"+str(institute_id)+"?user_id="+str(user['uuid']), institutePayload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Institute Details Updated Successfully!!!"}

def download_document(user_login_view, institute_unique_code, document_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/institute/download-document/" +document_id+ "?institute_id=" + str(institute_id))

def delete_document(user_login_view, institute_unique_code, document_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.post(user_login_view, "/2.0/institute/delete-document/"+str(institute_id)+"/"+document_id+ "?user_id="+str(user['uuid']), {})
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Institute Document Deleted Successfully!!!"}

def upload_document(user_login_view, institute_unique_code, document_type, document_name, document):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.upload_file(user_login_view, "/2.0/institute/upload-documents?institute_id="+str(institute_id) + "&user_id="+user['uuid'],{'file' : document, 'documentType' : document_type, 'documentName' : document_name})
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Document Uploaded Successfully!!!"}

def upload_standard_document(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, document_name, document):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.upload_file(user_login_view, "/2.0/institute/upload-standard-session-documents?institute_id="+str(institute_id) + "&academic_session_id="+str(academic_session_id) + "&standard_id="+str(standard_id)  + "&section_id="+str(section_id) + "&user_id="+user['uuid'],{'file' : document, 'documentName' : document_name})

def download_standard_document(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, document_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    return restclient.get_file(user_login_view, "/2.0/institute/download-standard-session-document/"+document_id+"?institute_id="+str(institute_id) + "&academic_session_id="+str(academic_session_id) + "&standard_id="+str(standard_id)  + "&section_id="+str(section_id))

def delete_standard_document(user_login_view, institute_unique_code, academic_session_id, standard_id, section_id, document_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    return restclient.post(user_login_view, "/2.0/institute/delete-standard-session-document/"+standard_id+"/"+document_id+"?institute_id="+str(institute_id) + "&academic_session_id="+str(academic_session_id) + "&section_id="+str(section_id) + "&user_id="+user['uuid'], {})

def get_institute_bank_account_details(user_login_view, institute_unique_code):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    response = restclient.get(user_login_view, "/2.0/institute/bank-accounts?institute_id="+str(institute_id))
    if not response.is_success():
        return None
    return response.get_data()

def add_bank_account(user_login_view, institute_unique_code, instituteBankAccountDetailsPayload):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    instituteBankAccountDetailsPayload["instituteId"] = institute_id
    response = restclient.post(user_login_view, "/2.0/institute/bank-account/"+str(institute_id)+"?user_id="+str(user['uuid']), instituteBankAccountDetailsPayload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Institute Bank Details Added Successfully!!!"}

def delete_bank_account(user_login_view, institute_unique_code, account_id):
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    user = user_login_view['user']
    response = restclient.delete(user_login_view, "/2.0/institute/bank-account/"+str(account_id)+"/inactive?institute_id="+str(institute_id) + "&user_id="+user['uuid'])
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Institute Bank Details Inactive Successfully!!!"}

def update_institute_bank_account_details(user_login_view, institute_unique_code, account_id, instituteBankAccountDetailsPayload):
    user = user_login_view['user']
    institute_id = get_institute_from_session_request(user_login_view, institute_unique_code)
    instituteBankAccountDetailsPayload["instituteId"] = institute_id
    response = restclient.put(user_login_view, "/2.0/institute/bank-account-details/"+str(account_id)+"?institute_id="+str(institute_id) + "&user_id="+str(user['uuid']), instituteBankAccountDetailsPayload)
    if not response.is_success():
        return {'success' : False , 'message' : response.get_error_message()}
    return {'success' : True , 'message' : "Institute Bank Details Updated Successfully!!!"}
