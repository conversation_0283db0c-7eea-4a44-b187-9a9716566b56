from django.contrib import admin
from django.urls import path
from django.urls import include
from institute_management.views import *

urlpatterns = [
    # Institute Management Home Page
    path('dashboard', dashboard_view, name='dashboard_view'),
    path('home', home_page_view, name='home_page_view'),
    path('session-home/<academic_session_id>', home_page_session_view, name='home_page_session_view'),

    path('manage-sections-home', manage_sections_home_view, name='manage_sections_home_view'),
    path('manage-sections/<academic_session_id>', manage_sections_view, name='manage_sections_view'),
    path('update-standards/<academic_session_id>', update_standards_view, name='update_standards_view'),
    path('bulk-update-students-section/<academic_session_id>', update_standard_section_view, name='update_standard_section_view'),
    path('bulk-add-students-section/<academic_session_id>', add_standard_section_view, name='add_standard_section_view'),
    path('bulk-delete-students-section/<academic_session_id>/<standard_id>/<section_ids>/<student_assign_section_id>', delete_standard_section_view, name='delete_standard_section_view'),
    path('class-students/<academic_session_id>', manage_sections_view, name='manage_sections_view'),
    path('class-students-sections-list/<academic_session_id>', manage_sections_view, name='manage_sections_view'),

    path('manage-class-teachers-home', manage_class_teachers_home_view, name='manage_class_teachers_home_view'),
    path('manage-class-teachers/<academic_session_id>', manage_class_teachers_view, name='manage_class_teachers_view'),
    path('update-class-teacher-details/<academic_session_id>/<standard_id>/<staff_id>', update_class_teacher_details_view, name='update_class_teacher_details_view'),
    path('delete-class-teacher-details/<academic_session_id>/<standard_id>', delete_class_teacher_details_view, name='delete_class_teacher_details_view'),

    path('manage-institute-details-home', manage_institute_details_home_view, name='manage_institute_details_home_view'),
    path('manage-institute-details', manage_institute_details_view, name='manage_institute_details_view'),
    path('update-institute', update_institute_details_home, name='update_institute_details_home'),

    path('manage-institute-logo-home', manage_institute_logo_home_view, name='manage_institute_logo_home_view'),
    path('manage-institute-logo', manage_institute_logo_view, name='manage_institute_logo_view'),
    path('document-download/<document_id>', document_download_view, name='document_download_view'),
    path('document-delete/<document_id>', document_delete_view, name='document_delete_view'),
    path('document-upload', document_upload_view, name='document_upload_view'),

    path('standard-document-upload', standard_document_upload_view, name='standard_document_upload_view'),
    path('standard-document-download/<standard_id>/<document_id>', standard_document_download_view, name='standard_document_download_view'),
    path('standard-document-delete/<standard_id>/<document_id>', standard_document_delete_view, name='standard_document_delete_view'),
    path('manage-institute-bank-account-details-home', institute_bank_account_details_home_view, name='institute_bank_account_details_home_view'),
    path('add-bank-account', add_bank_account_view, name='add_bank_account_view'),
    path('bank-account-details', bank_account_details_view, name='bank_account_details_view'),
    path('delete-bank-account/<account_id>', delete_bank_account_view, name='delete_bank_account_view'),
    path('update-bank-account/<account_id>', update_bank_account_view, name='update_bank_account_view'),
]
